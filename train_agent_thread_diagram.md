# Train Agent 线程关系图

## 系统架构图

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                   Train Agent 系统                                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────┐                                                                    │
│  │  主线程      │ ──────────────────────────────────────────────────────────────────┐ │
│  │ (main)      │                                                                  │ │
│  └─────────────┘                                                                  │ │
│         │                                                                        │ │
│         │ 创建并启动                                                               │ │
│         ▼                                                                        │ │
│  ┌─────────────────────────────────────────────────────────────────────────────┐  │ │
│  │                    Multi Train Manager                                      │  │ │
│  │                   (多火车管理器核心)                                          │  │ │
│  └─────────────────────────────────────────────────────────────────────────────┘  │ │
│         │                                                                        │ │
│         │ 启动所有子线程                                                           │ │
│         ▼                                                                        │ │
│  ┌─────────────────────────────────────────────────────────────────────────────┐  │ │
│  │                          网络通信层                                          │  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │  │ │
│  │  │ Epoll主循环  │  │  线程池      │  │ 网络消息发送 │  │ 任务重发线程 │        │  │ │
│  │  │   线程      │  │(最多20线程)  │  │   线程      │  │            │        │  │ │
│  │  │            │  │             │  │            │  │            │        │  │ │
│  │  │ UDP事件监听 │──│ 消息解析处理 │──│ 消息下发    │──│ 超时重发    │        │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │  │ │
│  └─────────────────────────────────────────────────────────────────────────────┘  │ │
│         │                                     │                                  │ │
│         │ 消息队列                             │ 消息队列                          │ │
│         ▼                                     ▼                                  │ │
│  ┌─────────────────────────────────────────────────────────────────────────────┐  │ │
│  │                          任务管理层                                          │  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │  │ │
│  │  │ 任务生成线程 │  │ 心跳检测线程 │  │ 任务状态报告 │  │ 设备重启线程 │        │  │ │
│  │  │            │  │            │  │   线程      │  │  (可选)     │        │  │ │
│  │  │ 生成新任务  │  │ 设备在线监控 │  │ 状态变化监控 │  │ 重启命令处理 │        │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │  │ │
│  └─────────────────────────────────────────────────────────────────────────────┘  │ │
│         │                                     │                                  │ │
│         │ 状态消息                             │ 异常消息                          │ │
│         ▼                                     ▼                                  │ │
│  ┌─────────────────────────────────────────────────────────────────────────────┐  │ │
│  │                          心跳管理层                                          │  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │  │ │
│  │  │心跳时间同步 │  │心跳消息处理 │  │ 里程信息统计 │  │ 火车信息汇总 │        │  │ │
│  │  │   线程      │  │   线程      │  │   线程      │  │   线程      │        │  │ │
│  │  │            │  │            │  │            │  │            │        │  │ │
│  │  │ 定时生成心跳 │  │ 心跳数据处理 │  │ 里程数据收集 │  │ 基本信息维护 │        │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘  └─────────────┘        │  │ │
│  └─────────────────────────────────────────────────────────────────────────────┘  │ │
│         │                                     │                                  │ │
│         │ ZMQ消息                             │ REP消息                          │ │
│         ▼                                     ▼                                  │ │
│  ┌─────────────────────────────────────────────────────────────────────────────┐  │ │
│  │                        调度通信层                                            │  │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                          │  │ │
│  │  │调度消息发送 │  │调度任务回复 │  │火车信息回复 │                          │  │ │
│  │  │   线程      │  │   线程      │  │   线程      │                          │  │ │
│  │  │            │  │            │  │            │                          │  │ │
│  │  │ ZMQ PUB发布 │  │ ZMQ REP响应 │  │ 信息查询响应 │                          │  │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘                          │  │ │
│  └─────────────────────────────────────────────────────────────────────────────┘  │ │
│                                                                                  │ │
└──────────────────────────────────────────────────────────────────────────────────┘ │
                                                                                     │
                                                                                     │
┌─────────────────────────────────────────────────────────────────────────────────────┘
│                                外部系统接口
├─────────────────────────────────────────────────────────────────────────────────────┐
│                                                                                     │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐          │
│  │   调度器     │    │  核心服务    │    │  火车设备    │    │  配置文件    │          │
│  │ (Scheduler) │    │(Core Server)│    │ (Train Dev) │    │(Config File)│          │
│  │             │    │             │    │             │    │             │          │
│  │ ZMQ REQ/PUB │    │ ZMQ REP     │    │ UDP通信     │    │ JSON文件    │          │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘          │
│         ▲                   ▲                   ▲                   ▲              │
│         │                   │                   │                   │              │
└─────────┼───────────────────┼───────────────────┼───────────────────┼──────────────┘
          │                   │                   │                   │
          │                   │                   │                   │
          ▼                   ▼                   ▼                   ▼
   ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
   │  任务接收    │    │  信息查询    │    │  设备通信    │    │  配置读写    │
   │             │    │             │    │             │    │             │
   │ 调度任务处理  │    │ 火车列表获取  │    │ 状态上报     │    │ 里程信息存储  │
   └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## 线程间通信关系图

```
                    ┌─────────────────────────────────────────┐
                    │              消息队列系统                │
                    └─────────────────────────────────────────┘
                                        │
        ┌───────────────────────────────┼───────────────────────────────┐
        │                               │                               │
        ▼                               ▼                               ▼
┌─────────────┐                ┌─────────────┐                ┌─────────────┐
│ 网络消息队列 │                │ 任务消息队列 │                │ 心跳消息队列 │
│m_net_msg_   │                │m_scheduler_ │                │m_dev_down   │
│queue        │                │task_msg     │                │link_hb_msg  │
└─────────────┘                └─────────────┘                └─────────────┘
        │                               │                               │
        │                               │                               │
        ▼                               ▼                               ▼
┌─────────────┐                ┌─────────────┐                ┌─────────────┐
│网络消息发送 │                │ 任务生成线程 │                │心跳消息处理 │
│   线程      │                │             │                │   线程      │
└─────────────┘                └─────────────┘                └─────────────┘

                    ┌─────────────────────────────────────────┐
                    │              互斥锁系统                  │
                    └─────────────────────────────────────────┘
                                        │
        ┌───────────────────────────────┼───────────────────────────────┐
        │                               │                               │
        ▼                               ▼                               ▼
┌─────────────┐                ┌─────────────┐                ┌─────────────┐
│ 火车操作锁   │                │ 任务操作锁   │                │ 重启信息锁   │
│m_train_mtx  │                │m_task_mtx   │                │m_reset_info │
│             │                │             │                │_mtx         │
└─────────────┘                └─────────────┘                └─────────────┘
        │                               │                               │
        │                               │                               │
        ▼                               ▼                               ▼
┌─────────────┐                ┌─────────────┐                ┌─────────────┐
│ 设备数据保护 │                │ 任务队列保护 │                │ 重启状态保护 │
│             │                │             │                │             │
└─────────────┘                └─────────────┘                └─────────────┘

                    ┌─────────────────────────────────────────┐
                    │              ZMQ通信系统                 │
                    └─────────────────────────────────────────┘
                                        │
        ┌───────────────────────────────┼───────────────────────────────┐
        │                               │                               │
        ▼                               ▼                               ▼
┌─────────────┐                ┌─────────────┐                ┌─────────────┐
│  PUB Socket │                │  REP Socket │                │  REQ Socket │
│             │                │             │                │             │
│ 状态消息发布 │                │ 任务接收处理 │                │ 数据请求查询 │
└─────────────┘                └─────────────┘                └─────────────┘
        │                               │                               │
        │                               │                               │
        ▼                               ▼                               ▼
┌─────────────┐                ┌─────────────┐                ┌─────────────┐
│调度消息发送 │                │调度任务回复 │                │火车信息汇总 │
│   线程      │                │   线程      │                │   线程      │
└─────────────┘                └─────────────┘                └─────────────┘
```

## 数据流向图

```
外部调度器 ──REQ──┐                                    ┌──PUB──▶ 外部订阅者
              │                                    │
              ▼                                    │
        ┌─────────────┐                    ┌─────────────┐
        │调度任务回复 │                    │调度消息发送 │
        │   线程      │                    │   线程      │
        └─────────────┘                    └─────────────┘
              │                                    ▲
              │ 任务消息队列                        │ 状态消息队列
              ▼                                    │
        ┌─────────────┐                    ┌─────────────┐
        │ 任务生成线程 │                    │ 心跳检测线程 │
        └─────────────┘                    └─────────────┘
              │                                    ▲
              │ 网络消息队列                        │ 设备状态
              ▼                                    │
        ┌─────────────┐                    ┌─────────────┐
        │网络消息发送 │                    │  线程池      │
        │   线程      │                    │ (消息解析)   │
        └─────────────┘                    └─────────────┘
              │                                    ▲
              │ UDP消息                            │ UDP消息
              ▼                                    │
        ┌─────────────┐                    ┌─────────────┐
        │  火车设备    │ ◄──────────────────│ Epoll主循环 │
        │             │      网络通信       │   线程      │
        └─────────────┘                    └─────────────┘

配置文件 ◄──────────────────────────────────────────────── 里程信息统计线程
                            定期写入里程数据

核心服务 ◄──REQ──────────────────────────────────────────── 火车信息汇总线程
        ──REP──────────────────────────────────────────▶ 火车信息回复线程
                            信息查询与响应
```

## 线程生命周期图

```
程序启动
    │
    ▼
┌─────────────┐
│  主线程      │ ──────┐
│ 初始化系统   │       │
└─────────────┘       │
    │                 │
    ▼                 │
┌─────────────┐       │
│Multi Train  │       │
│Manager 初始化│       │
└─────────────┘       │
    │                 │
    ▼                 │
┌─────────────┐       │
│ 网络初始化   │       │
│ UDP/Epoll   │       │
└─────────────┘       │
    │                 │
    ▼                 │
┌─────────────┐       │
│ ZMQ初始化    │       │
│ 调度通信     │       │
└─────────────┘       │
    │                 │
    ▼                 │
┌─────────────┐       │
│ 线程池启动   │       │
│ (20个工作线程)│       │
└─────────────┘       │
    │                 │
    ▼                 │
┌─────────────┐       │
│ 启动所有     │       │
│ 功能线程     │       │
└─────────────┘       │
    │                 │
    ▼                 │
┌─────────────┐       │
│ Epoll主循环  │       │
│ (阻塞运行)   │       │
└─────────────┘       │
                      │
                      │ 程序运行期间
                      │ 所有线程并发执行
                      │
                      ▼
                ┌─────────────┐
                │ 程序终止     │
                │ 资源清理     │
                └─────────────┘
```

## 关键线程优先级

### 高优先级线程 (核心功能)
1. **Epoll主循环线程** - 网络事件处理
2. **线程池** - 消息解析处理  
3. **网络消息发送线程** - 设备通信
4. **心跳检测线程** - 设备状态监控

### 中优先级线程 (业务逻辑)
5. **任务生成线程** - 任务处理
6. **调度任务回复线程** - 外部接口
7. **调度消息发送线程** - 状态发布
8. **任务重发线程** - 可靠性保证

### 低优先级线程 (辅助功能)
9. **心跳时间同步线程** - 定时任务
10. **火车信息汇总线程** - 信息维护
11. **任务状态报告线程** - 状态监控
12. **里程信息统计线程** - 数据统计

### 可选线程
13. **设备重启线程** - 特殊场景
14. **火车信息回复线程** - 查询服务
15. **心跳消息处理线程** - 心跳专用

这个架构设计体现了高并发、高可靠性的分布式系统特点，通过合理的线程分工和消息队列机制，实现了火车设备的高效管理。